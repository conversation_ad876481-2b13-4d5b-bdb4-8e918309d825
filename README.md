# 命理学研习社 - 智慧传承，数字化学习平台

<div align="center">

![License](https://img.shields.io/badge/license-MIT-green.svg)
![React](https://img.shields.io/badge/React-18-blue.svg)
![Next.js](https://img.shields.io/badge/Next.js-14-black.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5-blue.svg)
![Python](https://img.shields.io/badge/Python-3.9+-yellow.svg)

*融合千年智慧与现代科技，打造专业的命理学教育平台*

</div>

## 📖 项目简介

命理学研习社是一个现代化的命理学教育平台，致力于传承和发扬中华传统文化中的命理学智慧。平台提供八字、紫微斗数、梅花易数三大方向的系统化学习方案，集在线测算、课程教育、经典阅读、案例分析于一体。

### 🎯 核心理念

- **传承文化** - 保护和传承中华命理学传统文化
- **科学学习** - 提供系统化、科学化的学习方法
- **技术赋能** - 运用现代技术提升学习体验
- **知识共享** - 构建命理学爱好者的学习社区

## ✨ 主要功能

### 🔮 在线测算
- **八字命理** - 基于出生时辰的四柱八字分析
- **紫微斗数** - 精准的星盘排布与命运解析
- **梅花易数** - 数理推演的占卜预测方法

### 📚 学习系统
- **课程学习** - 从入门到精通的系统化课程
- **经典书籍** - 古典典籍与现代白话对照阅读
- **案例解析** - 丰富的实战案例库与分析

### 👥 社区功能
- **案例分享** - 用户可创建和分享学习案例
- **学习记录** - 个人学习进度跟踪
- **收藏管理** - 收藏喜爱的内容和课程

### 📂 案例管理系统
- **个人档案展示** - 用户本人档案头部展示，包含今日运势
- **分类案例管理** - 我的案例分为家人、朋友、同学、同事、其他五大类
- **系统案例库** - 提供经典历史人物命理案例参考
- **优雅卡片设计** - 采用性别主题色彩的渐变卡片，突出姓名展示

## 🆕 最近更新

### 错误处理系统优化 (2024-12)
- **🛡️ 全新错误处理架构** - 重构全局错误处理系统，提供更友好的用户体验
- **📱 Toast 友好提示** - 替换原生弹窗和控制台错误，使用优雅的 Toast 提示
- **🎯 智能错误分类** - 自动识别网络、认证、API服务器、语法、运行时等不同错误类型
- **🔧 React 错误边界** - 新增 ErrorBoundary 组件，防止单个组件错误导致整个应用崩溃
- **⚡ 页面无缝体验** - 错误不再导致页面崩溃，用户可继续使用其他功能
- **🎨 用户友好信息** - 将技术性错误转换为用户易懂的提示信息
- **🔄 自动错误恢复** - 为语法错误提供缓存清理解决方案，API错误提供重试机制
- **📊 错误分级处理** - 关键错误显示详细指引，非关键错误静默处理
- **🎪 演示测试页面** - 提供错误处理演示页面，便于测试和展示功能

### 案例管理系统全新上线 (2024-12)
- **🎨 全新案例页面设计** - 基于现代移动端设计规范的优雅界面
- **📱 个人档案头部** - 采用清新绿色渐变设计，展示个人信息和今日运势
- **🃏 智能卡片系统** - 性别主题色彩的竖版卡片设计，突出用户姓名
- **📊 分类管理功能** - 支持家人、朋友、同学、同事、其他五大分类
- **🏛️ 系统案例库** - 提供历史名人经典案例供学习参考
- **🎯 移动端优化布局** - 采用3列网格布局，遵循移动端设计最佳实践
- **🔗 SWR数据管理** - 使用useCaseList Hook实现高效的数据获取和缓存
- **🔍 智能数据筛选** - 基于relation_type自动区分个人案例和系统案例
- **⚡ 并发数据加载** - 同时获取多个数据源，优化加载性能
- **🛡️ 完善错误处理** - 加载状态、错误重试、空状态展示
- **🎭 多格式兼容** - 支持中文和英文性别格式的自动转换
- **✨ 视觉交互优化** - 悬浮效果、缩放动画、毛玻璃背景等现代化设计元素

### 阅读体验优化 (2024-12)
- **优化页面加载体验** - 改善书籍阅读页面的加载状态显示，提供连续平滑的用户体验
  - **友好的加载提示** - 显示"正在加载章节内容"而非错误暗示，提供明确的状态反馈
  - **视觉连续性** - 确保从恢复阅读进度到内容显示的整个过程视觉上连续平滑
  - **统一的加载样式** - BookReader和PaginatedView使用一致的加载动画和文案
  - **简化状态管理** - 避免过度工程化，保持逻辑简单清晰的同时优化用户感知
  - **统一页面切换方式** - 封面页与正文页面之间的切换改为直接显示/隐藏，与正文翻页方式一致
  - **保持内容连续性** - 在分页重新计算期间显示之前的内容，避免空白闪现
- **平衡技术实现与用户体验** - 在保持代码简单可维护的前提下，最大化提升用户体验

## 🏗️ 技术架构

### 前端技术栈
```
React 18          - 现代化UI框架
Next.js 14        - 全栈React框架
TypeScript        - 类型安全的JavaScript
Tailwind CSS      - 实用优先的CSS框架
Shadcn/ui         - 高质量React组件库
NextAuth.js       - 身份验证解决方案
```

### 后端技术栈
```
Python 3.9+       - 后端主要开发语言
FastAPI           - 现代异步Web框架
SQLAlchemy        - ORM数据库工具
Alembic           - 数据库迁移工具
Redis             - 缓存和会话存储
```

### 数据存储
```
PostgreSQL        - 主要关系型数据库
MongoDB           - 文档型数据存储
Redis             - 缓存和临时数据
```

## 📱 平台特色

### 桌面端体验
- **专业界面** - 清晰的功能布局和导航
- **高效操作** - 快速访问各种功能模块
- **详细展示** - 充分利用大屏幕显示详细信息

### 移动端优化
- **原生体验** - 遵循iOS和Android设计规范
- **智能导航** - 底部Tab导航 + 顶部智能返回
- **首次引导** - 全新优雅的毛玻璃Landing页面引导用户选择
- **视觉美学** - 渐变背景 + 毛玻璃效果，营造沉浸式体验
- **记忆功能** - 记住用户偏好，下次直接进入
- **触摸优化** - 适配移动设备的交互体验

### 核心亮点
- **响应式设计** - 完美适配各种设备屏幕
- **安全区域适配** - 支持iPhone刘海屏等特殊屏幕
- **性能优化** - 代码分割、懒加载、缓存策略
- **无障碍支持** - 支持屏幕阅读器和键盘导航
- **智能错误处理** - 全局错误捕获与友好提示系统

## 🚀 快速开始

### 环境要求
```bash
Node.js >= 16.0.0
Python >= 3.9
PostgreSQL >= 13
MongoDB >= 5.0
Redis >= 6.0
```

### 安装依赖

**前端依赖**
```bash
cd client
npm install
```

**后端依赖**
```bash
cd server
pip install -r requirements.txt
```

### 环境配置

**前端环境变量** (`.env.local`)
```env
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:8000
```

**后端环境变量** (`.env`)
```env
DATABASE_URL=postgresql://user:password@localhost:5432/fate_explorer
MONGODB_URL=mongodb://localhost:27017/fate_explorer
REDIS_URL=redis://localhost:6379
SECRET_KEY=your-secret-key
```

### 启动服务

**启动后端服务**
```bash
cd server
python -m uvicorn app.main:app --reload --port 8000
```

**启动前端服务**
```bash
cd client
npm run dev
```

访问 `http://localhost:3000` 开始使用平台。

## 📁 项目结构

```
fate_explorer/
├── client/                 # 前端代码
│   ├── src/
│   │   ├── app/           # Next.js App Router页面
│   │   │   ├── components/    # React组件
│   │   │   │   ├── layout/    # 布局组件
│   │   │   │   │   ├── desktop/  # 桌面端布局
│   │   │   │   │   └── mobile/   # 移动端布局
│   │   │   │   ├── ui/        # 基础UI组件
│   │   │   │   └── icons/     # 图标组件
│   │   │   ├── lib/           # 工具函数
│   │   │   ├── styles/        # 样式文件
│   │   │   └── types/         # TypeScript类型定义
│   │   ├── public/            # 静态资源
│   │   └── package.json       # 前端依赖配置
│   ├── server/                 # 后端代码
│   │   ├── app/
│   │   │   ├── api/           # API路由
│   │   │   ├── core/          # 核心配置
│   │   │   ├── db/            # 数据库相关
│   │   │   ├── models/        # 数据模型
│   │   │   ├── schemas/       # Pydantic模式
│   │   │   └── services/      # 业务逻辑
│   │   ├── requirements.txt   # Python依赖
│   │   └── main.py           # 应用入口
│   ├── data/                   # 数据文件
│   │   ├── books/             # 电子书资源
│   │   └── scripts/           # 数据处理脚本
│   ├── docs/                   # 文档
│   └── README.md              # 项目说明
```

## 🎨 设计系统

### 移动端布局架构

**导航设计**
- **顶部导航** - 页面标题 + 智能返回 + 用户头像
- **底部导航** - 八字 | 紫微 | ➕ | 梅花 | 我的
- **Landing页面** - 全新毛玻璃风格的首次访问引导页

**核心组件**
- `MobileRootLayout` - 移动端根布局
- `MobileHeader` - 智能顶部导航栏
- `MobileBottomNavigation` - 底部Tab导航
- `LandingScreen` - 毛玻璃风格的首次访问引导页
- `WelcomeScreen` - 备用的欢迎页面组件
- `TabContent` - 通用标签页内容

**视觉特色**
- **毛玻璃效果** - 使用 `backdrop-filter: blur()` 实现现代化视觉效果
- **渐变背景** - 多层次渐变营造优雅的视觉层次
- **智能适配** - 支持深色模式和各种屏幕尺寸
- **交互反馈** - 平滑的缩放和过渡动画

### 路由架构设计

**高性能设备适配系统**
- **单点检测** - Middleware检测设备类型，Layout层统一处理分发逻辑
- **零重复判断** - 避免在多个组件中重复读取Headers，提升性能
- **智能路由** - 移动端根路径显示Landing页面，其他路径使用正常布局
- **内容分离** - 桌面端Page纯净化，移动端逻辑完全在Layout层处理
- **向后兼容** - `/m` 路径自动重定向到根路径，确保现有链接可用

### 用户体验流程

**移动端流程**
1. **首次访问** → 自动显示毛玻璃Landing页面 → 选择功能方向 → 记录偏好 → 进入功能页面
2. **再次访问** → Landing页面检测localStorage → 直接跳转到上次选择的功能页面
3. **功能切换** → 底部导航随时切换不同方向
4. **内容创建** → 点击居中"+"按钮创建案例

**桌面端流程**
1. **访问首页** → 显示传统桌面端主页 → 浏览功能介绍 → 点击进入相应功能
2. **导航使用** → 顶部导航栏和侧边栏快速切换功能
3. **多窗口操作** → 支持多标签页同时使用不同功能

### 架构执行流程

**高性能设备适配流程**：
```
用户访问任意路径
    ↓
Middleware检测设备类型 (设置x-device-type, x-pathname)
    ↓
Layout读取Headers (仅一次读取)
    ↓
移动端判断:
  - 根路径 "/" → 直接渲染LandingScreen
  - 其他路径 → MobileLayout + Page内容
桌面端: DesktopLayout + Page内容
```

**性能优势**：
- ✅ **单点检测** - Headers只在Layout层读取一次
- ✅ **零重复** - 避免Page层重复判断设备类型  
- ✅ **高效路由** - 移动端Landing页面直接渲染，无额外跳转
- ✅ **代码分离** - 桌面端Page内容纯净，移动端逻辑隔离

### 最新更新 (Latest Updates)

#### 2024年1月 - 移动端视觉体验升级 🎨
- ✨ **全新Landing页面** - 采用毛玻璃效果设计，提供更优雅的首次访问体验
- 🎨 **视觉美学提升** - 渐变背景 + 毛玻璃卡片，营造沉浸式的现代化界面
- 📱 **移动端优化** - 专为移动设备优化的交互体验和视觉效果
- 🏗️ **架构优化 2.0** - Layout层统一处理设备检测，避免重复判断，提升性能
- 🌙 **深色模式支持** - 完美适配系统深色模式主题
- ⚡ **性能优化** - 单点设备检测，减少Headers读取次数
- 🔄 **向后兼容** - `/m` 路径自动重定向，保持现有链接可用

## 🔧 开发指南

### 代码规范

**命名约定**
- 组件文件：`kebab-case.tsx`
- 组件名称：`PascalCase`
- Hook函数：`useCamelCase`
- 变量/函数：`camelCase`

**TypeScript规范**
- 所有组件必须有类型定义
- 使用接口定义Props和状态
- 开启严格模式检查

**样式规范**
- 优先使用Tailwind CSS类
- 避免内联样式
- 使用CSS变量管理主题

### 提交规范

```bash
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式化
refactor: 重构代码
test: 测试相关
chore: 构建过程或辅助工具变更
```

## 🚀 部署说明

### 生产环境构建

**前端构建**
```bash
cd client
npm run build
npm start
```

**后端部署**
```bash
cd server
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### Docker部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

<div align="center">

**用科技传承智慧，让命理学习更简单** ✨

Made with ❤️ by 命理学研习社团队

</div> 