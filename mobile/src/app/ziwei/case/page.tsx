"use client"

import React, { useState, use<PERSON>em<PERSON>, useCallback } from "react"
import { useRouter, usePathname } from "next/navigation"
import PersonalProfileHeader from "@/components/case/personal-profile-header"
import CaseCard from "@/components/case/case-card"
import { SecionTab } from "@/components/ui/top-tabs"
import { CaseProfile, PersonalProfile, CaseGroup } from "@/types/case"
import { RelationType, CaseType } from "@/types/enums"
import { useCaseList, useDeleteCase } from "@/hooks/use-api"
import { useAuth } from "@/hooks/use-auth"
import { LoginPrompt } from "@/components/auth/login-prompt"
import { ClipboardMinus, FileUser } from 'lucide-react';
import { useLongPress } from "@/hooks/use-long-press"
import { Button } from "@/components/ui/button"
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure
} from "@heroui/modal";

// 标签页配置
const tabs = [
  { id: "mine", label: "我的" },
  { id: "system", label: "系统" }
]

export default function CasePage() {
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, isLoading: authLoading } = useAuth()
  const [activeTab, setActiveTab] = useState<string>("mine")
  const [selectedItem, setSelectedItem] = useState<CaseProfile | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false)
  const {isOpen, onOpen, onOpenChange} = useDisclosure();


  // 获取我的案例数据（除了系统案例）
  const { 
    cases: myCases, 
    isLoading: myLoading, 
    isError: myError 
  } = useCaseList({ 
    case_type: CaseType.ZIWEI,
    page: 1,
    page_size: 100  // 获取足够多的数据
  })
  
  // 获取系统案例数据
  const { 
    cases: systemCases, 
    isLoading: systemLoading, 
    isError: systemError 
  } = useCaseList({ 
    relation_type: RelationType.SYSTEM,  // 系统案例
    case_type: CaseType.ZIWEI,
    page: 1,
    page_size: 100
  })
  // 处理个人档案数据
  const personalProfile: PersonalProfile | null = useMemo(() => {
    if (!myCases || myCases.length === 0) return null
    
    // 找到关系类型为"我"的案例作为个人档案
    const myCase = myCases.find(c => c.relation_type === RelationType.ME)
    if (!myCase) return null
    
    return {
      ...myCase,
      today_fortune: "今日紫微入财帛宫，利于投资理财，但需谨慎行事。感情运势平稳，宜多与家人沟通。"
    }
  }, [myCases])
  
  // 将我的案例按关系类型分组
  const myGroups: CaseGroup[] = useMemo(() => {
    if (!myCases) return []
    
    // 过滤掉自己的档案和系统案例，只保留其他关系类型
    const otherCases = myCases.filter(c => 
      c.relation_type !== RelationType.ME && 
      c.relation_type !== RelationType.SYSTEM
    )
    
    // 按关系类型分组
    const groups: { [key: string]: CaseProfile[] } = {}
    const relationTitles: { [key: string]: string } = {
      [RelationType.FAMILY]: "家人",
      [RelationType.FRIEND]: "朋友", 
      [RelationType.CLASSMATE]: "同学",
      [RelationType.COLLEAGUE]: "同事",
      [RelationType.OTHER]: "其他"
    }
    
    otherCases.forEach(caseItem => {
      const relationType = caseItem.relation_type
      if (!groups[relationType]) {
        groups[relationType] = []
      }
      groups[relationType].push(caseItem)
    })
    
    // 转换为CaseGroup格式
    return Object.entries(groups).map(([relationType, profiles]) => ({
      title: relationTitles[relationType] || relationType,
      relation_type: relationType as RelationType,
      profiles,
      count: profiles.length
    }))
  }, [myCases])
  
  // 使用通用长按管理器
  const { bind } = useLongPress({
    threshold: 600,
    onClick: (id) => {
      router.push(`/ziwei/case/detail?case_id=${id}`)
    },
    onLongPress: (id) => {
      const item = (activeTab === "mine" ? 
      myCases.find((i) => i.case_id === id) : systemCases.find((i) => i.case_id === id)
    )
      if (item) {
        setSelectedItem(item)
        onOpen();
      }
    },
  })

  const handleEdit = useCallback(() => {
    if (!selectedItem) return
    setIsDialogOpen(false)
    router.push(`/ziwei/case/edit?case_id=${selectedItem.case_id}`)
  }, [selectedItem, router])
  
  // 事件处理
  const handlePersonalClick = () => {
    if (personalProfile) {
      router.push(`/ziwei/case/detail?case_id=${personalProfile.case_id}`)
    }
  }
  // const { trigger: deleteCase } = useDeleteCase()

  // const handleDeleteCase = (caseId: string) => {
  //   deleteCase({ caseId })
  // }

  const handleCaseClick = (profile: CaseProfile) => {
    // TODO: 实现案例详情页跳转
    console.log('案例点击:', profile.user_name)
    // router.push(`/ziwei/case/${profile.case_id}`)
  }

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId)
  }

  // 计算整体加载状态
  const hasError = myError || systemError

  // 错误状态
  if (hasError) {
    return (
      <div className="bg-gradient-to-br from-slate-50 via-gray-50 to-stone-50 min-h-screen">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center space-y-4 p-6">
            <div className="text-red-500 text-5xl">⚠️</div>
            <p className="text-gray-600">加载案例数据失败，请稍后重试</p>
            <button 
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* 个人档案头部 - 只有在已登录且在mine标签页时显示 */}
      {activeTab === "mine" && isAuthenticated && (
        <div className="p-4 ">
          {personalProfile && (
            <PersonalProfileHeader 
              profile={personalProfile}
              onClick={handlePersonalClick}
            />
          )}
        </div>
      )}

      {/* 标签页切换 */}
      <div className="px-4">
        <SecionTab 
          tabs={tabs}
          activeTab={activeTab}
          onTabClick={handleTabClick}
        />
      </div>

      {/* 内容区域 */}
      <div className="px-4 pb-6">
        {activeTab === "mine" && (
          <div>
            {authLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 border-2 border-gray-300 border-t-primary rounded-full animate-spin"></div>
                  <span className="text-gray-600">检查登录状态...</span>
                </div>
              </div>
            ) : !isAuthenticated ? (
              <LoginPrompt 
                title="查看我的紫微案例"
                description="登录后即可查看您的紫微斗数个人案例和学习记录"
                returnUrl={pathname}
              />
            ) : myLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 border-2 border-gray-300 border-t-primary rounded-full animate-spin"></div>
                  <span className="text-gray-600">加载案例数据...</span>
                </div>
              </div>
            ) : (
              <div className="space-y-8" {...bind}>
                {myGroups.length > 0 ? (
                  myGroups.map((group) => (
                    <div key={group.title} className="space-y-4">
                      {/* 分组标题 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                        <span className="h-6 w-1 bg-accent rounded-full mr-3"></span>
                        <h2 className="text-lg font-semibold text-gray-800 tracking-wide">
                          {group.title}
                        </h2>
                        </div>
                        <div className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                          {group.count}
                        </div>
                      </div>

                      {/* 案例卡片网格 - 移动端3列布局 */}
                      <div className="grid grid-cols-3 gap-3 sm:gap-4">
                        {group.profiles.map((profile) => (
                          <CaseCard 
                            key={profile.case_id}
                            profile={profile}
                            onClick={handleCaseClick}
                          />
                        ))}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-12">
                    <ClipboardMinus className="w-10 h-10 mx-auto mb-2" />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">这里是空的</h3>
                    <p className="text-sm text-gray-500">点击下方&ldquo;测&rdquo;按钮创建新的案例</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {activeTab === "system" && (
          <div className="space-y-4">
            {/* 系统案例库标题 */}
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800 tracking-wide">
                经典案例
              </h2>
              <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                {systemCases?.length || 0}
              </span>
            </div>

            {/* 系统案例卡片网格 - 移动端3列布局 */}
            {systemCases && systemCases.length > 0 ? (
              <div className="grid grid-cols-3 gap-3 sm:gap-4">
                {systemCases.map((profile) => (
                  <CaseCard 
                    key={profile.case_id}
                    profile={profile}
                    onClick={handleCaseClick}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <FileUser className="w-10 h-10 mx-auto mb-2" />
                <h3 className="text-lg font-medium text-gray-600 mb-2">暂无系统案例</h3>
                <p className="text-sm text-gray-500">系统案例库正在建设中</p>
              </div>
            )}
          </div>
        )}
      </div>

      <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">管理档案</ModalHeader>
              <ModalBody>
                <p>
                  是否要重新修改当前案例？
                </p>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="outline" onClick={onClose}>
                  取消
                </Button>
                <Button color="primary" onClick={handleEdit}>
                  编辑
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  )
}

