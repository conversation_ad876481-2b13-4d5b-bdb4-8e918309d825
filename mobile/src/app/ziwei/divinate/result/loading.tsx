"use client"
import { useState, useEffect } from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { Card } from "@/components/ui/card"

export default function Loading() {
  const [loadingProgress, setLoadingProgress] = useState(0)
  const [loadingMessage, setLoadingMessage] = useState("正在解析出生信息...")

  useEffect(() => {
    const messages = [
      "正在解析出生信息...",
      "正在计算真太阳时...",
      "正在排列紫微星盘...",
      "正在计算十二宫位...",
      "正在安排星曜分布...",
      "正在生成命盘结果..."
    ]

    let currentIndex = 0
    let progress = 0

    const timer = setInterval(() => {
      progress += Math.random() * 15 + 5 // 每次增加 5-20%
      if (progress > 95) progress = 95 // 最大到95%，避免到100%但还在loading

      setLoadingProgress(progress)

      // 根据进度更新消息
      const messageIndex = Math.floor((progress / 100) * messages.length)
      if (messageIndex < messages.length && messageIndex !== currentIndex) {
        setLoadingMessage(messages[messageIndex])
        currentIndex = messageIndex
      }

      if (progress >= 95) {
        clearInterval(timer)
      }
    }, 300)

    return () => clearInterval(timer)
  }, [])

  // 宫位骨架组件
  const PalaceSkeleton = () => (
    <div className="h-40 p-2 flex flex-col justify-between">
      {/* 星耀区域 */}
      <div className="space-y-1">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-12" />
      </div>
      
      {/* 小限区域 */}
      <div className="my-2">
        <Skeleton className="h-3 w-24" />
      </div>

      <div className="flex justify-between items-end">
        {/* 左侧神煞 */}
        <div className="space-y-1">
          <Skeleton className="h-3 w-12" />
          <Skeleton className="h-3 w-12" />
          <Skeleton className="h-3 w-12" />
        </div>

        {/* 中间宫名和大限 */}
        <div className="text-center space-y-2">
          <Skeleton className="h-3 w-16" />
          <Skeleton className="h-5 w-12" />
        </div>

        {/* 右侧干支和长生 */}
        <div className="space-y-1 text-end">
          <Skeleton className="h-3 w-12" />
          <Skeleton className="h-3 w-8" />
          <Skeleton className="h-3 w-8" />
        </div>
      </div>
    </div>
  )

  // 中央信息区域骨架
  const CenterInfoSkeleton = () => (
    <div className="col-span-2 row-span-2 p-4 space-y-4">
      {/* 性别和五行 */}
      <div className="flex justify-center">
        <Skeleton className="h-6 w-32" />
      </div>

      {/* 时间信息 */}
      <div className="space-y-2">
        <div className="flex gap-4">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-32" />
        </div>
        <div className="flex gap-4">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-32" />
        </div>
      </div>

      {/* 四柱 */}
      <div className="space-y-2">
        <div className="flex justify-center gap-4">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-32" />
        </div>
      </div>

      {/* 命主身主 */}
      <div className="flex justify-between">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-24" />
      </div>

      {/* 四化 */}
      <div className="flex justify-between">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-32" />
      </div>
    </div>
  )

  return (
    <div className="container mx-auto p-4">
      {/* 进度指示器 */}
      <div className="mb-6 text-center">
        <div className="mb-2 text-lg font-medium text-gray-700">{loadingMessage}</div>
        <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
          <div 
            className="bg-linear-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${loadingProgress}%` }}
          ></div>
        </div>
        <div className="text-sm text-gray-500">{Math.round(loadingProgress)}% 完成</div>
      </div>

      <Card className="w-full max-w-2xl mx-auto">
        {/* 紫微斗数图表骨架 */}
        <div className="grid grid-cols-4 grid-rows-4 gap-0">
          {/* 第一行 */}
          <div className="border border-gray-200"><PalaceSkeleton /></div>
          <div className="border border-gray-200"><PalaceSkeleton /></div>
          <div className="border border-gray-200"><PalaceSkeleton /></div>
          <div className="border border-gray-200"><PalaceSkeleton /></div>

          {/* 第二行 */}
          <div className="border border-gray-200"><PalaceSkeleton /></div>
          <CenterInfoSkeleton />
          <div className="border border-gray-200"><PalaceSkeleton /></div>

          {/* 第三行 */}
          <div className="border border-gray-200"><PalaceSkeleton /></div>
          <div className="border border-gray-200"><PalaceSkeleton /></div>

          {/* 第四行 */}
          <div className="border border-gray-200"><PalaceSkeleton /></div>
          <div className="border border-gray-200"><PalaceSkeleton /></div>
          <div className="border border-gray-200"><PalaceSkeleton /></div>
          <div className="border border-gray-200"><PalaceSkeleton /></div>
        </div>
      </Card>
    </div>
  )
} 