import { CaseType, RelationType } from "./enums"

// 案例档案接口 - 用于展示 (兼容后端API)
export interface CaseProfile {
  case_id: string
  user_id: string
  user_name: string
  gender: "男" | "女" | "其他" | "male" | "female"
  birth_time_solar: string | Date
  birth_time_lunar: string
  birth_place: string
  bazi_year: string
  bazi_month: string
  bazi_day: string
  bazi_time: string
  divinate_result?: any
  comment?: string
  tags: string[] // 0-3个标签，每个不超过4个字
  relation_type: RelationType
  case_type: CaseType
  created_at?: string | Date
  updated_at?: string | Date
  
  // 为了向后兼容，保留原有的id字段
  id?: string
}

// 个人档案接口 - 包含今日运势
export interface PersonalProfile extends CaseProfile {
  today_fortune?: string // 今日运势
}

// 案例分组接口
export interface CaseGroup {
  title: string
  relation_type?: RelationType
  profiles: CaseProfile[]
  count: number
}

// 定义案例创建请求的接口
export interface CaseCreateRequest {
  user_name: string
  gender: string
  birth_time_solar: string  // 后端需要datetime，前端传本地时间字符串格式 YYYY-MM-DD HH:MM:SS
  birth_time_lunar: string
  bazi_year: string
  bazi_month: string
  bazi_day: string
  bazi_time: string
  birth_place: string
  // 是否使用真太阳时
  useTrueSolarTime: boolean
  // 是否是夏令时
  isDST: boolean
  // 是否使用早晚子时
  useEarlyOrLateNight: boolean
  // 经度
  longitude: number
  divinate_result?: any
  comment?: string
  relation_type: RelationType
  tags: string[]
  case_type: CaseType
}

// 定义案例更新请求的接口 - 匹配后端 CaseUpdate schema
export interface CaseUpdateRequest {
  user_name?: string
  gender?: string
  birth_time_solar?: string
  birth_time_lunar?: string
  bazi_year?: string
  bazi_month?: string
  bazi_day?: string
  bazi_time?: string
  birth_place?: string
  divinate_result?: any
  comment?: string
  relation_type?: RelationType
  tags?: string[]
}
