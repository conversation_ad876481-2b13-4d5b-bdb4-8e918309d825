import { astro } from "iztro";
import FunctionalAstrolabe from "iztro/lib/astro/FunctionalAstrolabe";
import { ZiweiDivinateRequest } from "@/types/user";

// 通过阳历获取星盘信息
export const getZiweiResult = (ziweiDivinateRequest: ZiweiDivinateRequest): FunctionalAstrolabe => {
  const astrolabe = astro.bySolar(ziweiDivinateRequest.divinateDate, 
                            ziweiDivinateRequest.timeIndex, 
                            ziweiDivinateRequest.gender === "male" ? "男" : "女") 
  return astrolabe;
}