"use client"

import * as React from "react"
import { CalendarIcon } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { formatDate, isValidDate } from "@/lib/utils"
import { getTimeList, getFortuneTimeZiwei, getTimeIndex } from "@/lib/astro-utils"
import FunctionalAstrolabe from "iztro/lib/astro/FunctionalAstrolabe"
import { ZiweiFortuneRequest, FortuneType } from "@/types/divinate_result"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"
import { zhCN } from "date-fns/locale"

interface Calendar24Props {
  astroData: FunctionalAstrolabe
  onSelectChange?: (fortuneType: FortuneType) => void
  onFortuneRequestChange?: (request: ZiweiFortuneRequest) => void
}

export interface FortuneTimeRef {
  getCurrentSelection: () => {
    selectedDecadal: number
    selectedYear: number
    selectedMonth: number
    selectedDate: Date | undefined
    selectedTimeIndex: number
    createFortuneRequest: (type: FortuneType, year?: number, month?: number, day?: number, timeIndex?: number) => ZiweiFortuneRequest | null
  }
  setDefaultValues: (fortuneRequest: ZiweiFortuneRequest) => void
}

export const Calendar24 = React.forwardRef<FortuneTimeRef, Calendar24Props>(
  ({ astroData, onSelectChange, onFortuneRequestChange }, ref) => {
    const [selectedDecadal, setSelectedDecadal] = React.useState<number>(-1)
    const [selectedYear, setSelectedYear] = React.useState<number>(-1)
    const [selectedMonth, setSelectedMonth] = React.useState<number>(-1)
    const [selectedDate, setSelectedDate] = React.useState<Date>()
    const [selectedTimeIndex, setSelectedTimeIndex] = React.useState<number>(-1)
    const [date, setDate] = React.useState<Date>()
    const [month, setMonth] = React.useState<Date>()
    const [open, setOpen] = React.useState(false)
    const [value, setValue] = React.useState("")

    // 获取时辰列表
    const timeList = getTimeList()
    
    // 获取大限数据并按岁数排序
    const fortuneTime = React.useMemo(() => {
      return getFortuneTimeZiwei(astroData).sort((a, b) => a.range[0] - b.range[0])
    }, [astroData])

    // 获取出生年和年龄范围
    const birthYear = React.useMemo(() => new Date(astroData.solarDate).getFullYear(), [astroData.solarDate])
    const maxAge = 120
    
    // 生成完整的年份列表（出生年至120岁）
    const fullYearList = React.useMemo(() => {
      return Array.from({ length: maxAge + 1 }, (_, i) => birthYear + i)
    }, [birthYear])

    // 根据年份找到对应的大限索引
    const findDecadalByYear = React.useCallback((year: number) => {
      const age = year - birthYear
      return fortuneTime.findIndex(decadal => 
        age >= decadal.range[0] - 1 && age <= decadal.range[1] - 1
      )
    }, [birthYear, fortuneTime])

    // 日期处理辅助函数：创建新日期并处理有效性
    const createValidDate = React.useCallback((year: number, month: number, day: number) => {
      const newDate = new Date(year, month - 1, day)
      
      // 检查日期是否有效（例如2月30日不存在）
      if (newDate.getMonth() === month - 1) {
        return newDate
      } else {
        // 如果日期无效，使用该月的15日作为后备
        return new Date(year, month - 1, 15)
      }
    }, [])

    // 更新所有日期相关状态的辅助函数
    const updateDateStates = React.useCallback((newDate: Date) => {
      setSelectedDate(newDate)
      setDate(newDate)
      setValue(formatDate(newDate))
      setMonth(newDate)
    }, [])

    // 初始化默认值为当前时间
    React.useEffect(() => {
      const now = new Date()
      const currentYear = now.getFullYear()
      const currentMonth = now.getMonth() + 1
      
      // 设置当前年份
      setSelectedYear(currentYear)
      
      // 设置当前月份
      setSelectedMonth(currentMonth)
      
      // 使用辅助函数设置当前日期
      updateDateStates(now)
      // 设置当前时间索引
      setSelectedTimeIndex(getTimeIndex(now.getHours()))
      
      // 根据当前年份找到对应的大限
      const decadalIndex = findDecadalByYear(currentYear)
      if (decadalIndex !== -1) {
        setSelectedDecadal(decadalIndex)
      }
    }, [findDecadalByYear, updateDateStates])

    // 生成运限请求对象
    const createFortuneRequest = React.useCallback((type: FortuneType, year?: number, month?: number, day?: number, timeIndex?: number): ZiweiFortuneRequest => {
      const targetYear = year ?? selectedYear
      const targetMonth = month ?? selectedMonth
      const targetDay = day ?? 15 // 默认30号
      const targetTimeIndex = timeIndex ?? 6 // 默认流时为午时
      // 根据类型决定使用的日期
      let requestDate: Date
      requestDate = new Date(targetYear, targetMonth - 1, targetDay)
      return {
        type,
        date: requestDate,
        timeIndex: targetTimeIndex
      }
    }, [selectedYear, selectedMonth])

    // 处理大限选择
    const handleDecadalChange = (value: string) => {
      const decadalIndex = parseInt(value)
      setSelectedDecadal(decadalIndex)
      
      // 自动设置流年为大限起始年
      const startYear = birthYear + fortuneTime[decadalIndex].range[0] - 1
      setSelectedYear(startYear)
      
      // 如果流日不为空，则用新流年+原流月+原流日作为流日的值并更新
      if (selectedDate) {
        const originalMonth = selectedMonth
        const originalDay = selectedDate.getDate()
        
        // 使用辅助函数创建有效日期并更新状态
        const newDate = createValidDate(startYear, originalMonth, originalDay)
        updateDateStates(newDate)
      }
      
      // 生成运限请求并传递
      const fortuneRequest = createFortuneRequest('大限', startYear, selectedMonth, selectedDate?.getDate(), selectedTimeIndex)
      onFortuneRequestChange?.(fortuneRequest)
      
      // 触发切换到大限tab
      onSelectChange?.('大限')
    }

    // 处理流年选择
    const handleYearChange = (value: string) => {
      const year = parseInt(value)
      setSelectedYear(year)
      
      // 自动更新大限
      const decadalIndex = findDecadalByYear(year)
      if (decadalIndex !== -1) {
        setSelectedDecadal(decadalIndex)
      }
      
      // 如果流日不为空，则同步更新流日的值为新流年+原流月值+原流日值
      if (selectedDate) {
        const originalMonth = selectedMonth
        const originalDay = selectedDate.getDate()
        
        // 使用辅助函数创建有效日期并更新状态
        const newDate = createValidDate(year, originalMonth, originalDay)
        updateDateStates(newDate)
      }
      
      // 生成运限请求并传递
      const fortuneRequest = createFortuneRequest('流年', year, selectedMonth, selectedDate?.getDate(), selectedTimeIndex)
      onFortuneRequestChange?.(fortuneRequest)
      
      // 触发切换到流年tab
      onSelectChange?.('流年')
    }

    // 处理日期选择
    const handleDateChange = (newDate: Date | undefined) => {
      if (!newDate) return

      // 使用辅助函数更新所有日期相关状态
      updateDateStates(newDate)

      // 更新年月选择
      const year = newDate.getFullYear()
      const month = newDate.getMonth() + 1
      const day = newDate.getDate()

      // 如果年份不同，更新流年和大限
      if (year !== selectedYear) {
        setSelectedYear(year)
        
        // 同步更新大限
        const decadalIndex = findDecadalByYear(year)
        if (decadalIndex !== -1 && decadalIndex !== selectedDecadal) {
          setSelectedDecadal(decadalIndex)
        }
      }

      // 如果月份不同，更新流月
      if (month !== selectedMonth) {
        setSelectedMonth(month)
      }
      
      // 生成运限请求并传递
      const fortuneRequest = createFortuneRequest('流日', year, month, day, selectedTimeIndex)
      onFortuneRequestChange?.(fortuneRequest)
      
      // 触发切换到流日tab
      onSelectChange?.('流日')
    }

    // 处理流月选择
    const handleMonthChange = (value: string) => {
      const month = parseInt(value)
      setSelectedMonth(month)
      
      // 如果流日不为空，则同步更新流日的值为新流月+原流日
      if (selectedDate) {
        const originalDay = selectedDate.getDate()
        
        // 使用辅助函数创建有效日期并更新状态
        const newDate = createValidDate(selectedYear, month, originalDay)
        updateDateStates(newDate)
      }
      
      // 生成运限请求并传递
      const fortuneRequest = createFortuneRequest('流月', selectedYear, month, selectedDate?.getDate())
      onFortuneRequestChange?.(fortuneRequest)
      
      // 触发切换到流月tab
      onSelectChange?.('流月')
    }

    // 处理流时选择
    const handleTimeChange = (value: string) => {
      const timeIndex = parseInt(value)
      setSelectedTimeIndex(timeIndex)
      
      // 生成运限请求并传递（使用实际选择的日期和时间索引）
      if (selectedDate) {
        const year = selectedDate.getFullYear()
        const month = selectedDate.getMonth() + 1
        const day = selectedDate.getDate()
        const fortuneRequest = createFortuneRequest('流时', year, month, day, timeIndex)
        onFortuneRequestChange?.(fortuneRequest)
        
        // 触发切换到流时tab
        onSelectChange?.('流时')
      }
    }

    React.useImperativeHandle(ref, () => ({
      getCurrentSelection: () => {
        // 检查是否有有效的选择（现在默认都有有效选择，因为初始化时已设置）
        const hasValidSelection = selectedYear !== -1 || selectedDecadal !== -1 || selectedDate !== undefined
        
        return {
          selectedDecadal,
          selectedYear,
          selectedMonth,
          selectedDate: selectedDate,
          selectedTimeIndex,
          createFortuneRequest: hasValidSelection 
            ? createFortuneRequest 
            : () => {
                return null
              }
        }
      },
      setDefaultValues: (fortuneRequest: ZiweiFortuneRequest) => {
        const requestDate = new Date(fortuneRequest.date)
        const year = requestDate.getFullYear()
        const month = requestDate.getMonth() + 1
        const timeIndex = getTimeIndex(requestDate.getHours())
        
        // 只有在当前没有选择值时才设置默认值
        if (selectedYear === -1) {
          setSelectedYear(year)
          
          // 根据年份找到对应的大限
          const decadalIndex = findDecadalByYear(year)
          if (decadalIndex !== -1 && selectedDecadal === -1) {
            setSelectedDecadal(decadalIndex)
          }
        }
        
        setSelectedMonth(month)
        setSelectedTimeIndex(timeIndex)
        // 使用辅助函数设置日期状态
        updateDateStates(requestDate)
      }
    }))

    return (
      <div className="px-2 w-full md:max-w-2xl">
        <div className="flex flex-col gap-2 text-xs md:text-sm">
          {/* 第一组：大限、流年、流月（同一行）*/}
          <div className="space-y-3">
            <div className="flex gap-2 md:gap-3">
              {/* 大限 */}
              <div className="flex-1">
                <Select 
                  value={selectedDecadal === -1 ? "" : selectedDecadal.toString()} 
                  onValueChange={(value) => {
                    handleDecadalChange(value)
                  }}
                >
                  <SelectTrigger className="w-full text-xs md:text-sm">
                    <SelectValue placeholder="请选择大限" />
                  </SelectTrigger>
                  <SelectContent className="text-xs md:text-sm">
                    {fortuneTime.map((decadal, index) => (
                      <SelectItem key={index} value={index.toString()}>
                        {decadal.range.join('-')}岁 {decadal.heavenlyStem}{decadal.earthlyBranch}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* 流年 */}
              <div className="flex-1">
                <Select 
                  value={selectedYear === -1 ? "" : selectedYear.toString()} 
                  onValueChange={handleYearChange}
                >
                  <SelectTrigger className="w-full text-xs md:text-sm">
                    <SelectValue placeholder="年份" />
                  </SelectTrigger>
                  <SelectContent className="text-xs md:text-sm">
                    {fullYearList.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}年
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* 流月 */}
              <div className="flex-1">
                <Select 
                  value={selectedMonth === -1 ? "" : selectedMonth.toString()} 
                  onValueChange={handleMonthChange}
                  disabled={selectedYear === -1}
                >
                  <SelectTrigger className="w-full text-xs md:text-sm">
                    <SelectValue placeholder="月份" />
                  </SelectTrigger>
                  <SelectContent className="text-xs md:text-sm">
                    {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                      <SelectItem key={month} value={month.toString()}>{month}月</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* 第二组：流日、流时 */}
          <div className="space-y-1">
            <div className="flex gap-2 md:gap-3">
              {/* 流日 */}
              <div className="flex-1">
                <div className="relative">
                  <Input
                    id="date"
                    value={selectedDate ? formatDate(selectedDate) : value}
                    placeholder="选择日期"
                    className="pr-10 w-full text-xs md:text-sm"
                    onChange={(e) => {
                      const newDate = new Date(e.target.value)
                      if (isValidDate(newDate)) {
                        handleDateChange(newDate)
                      } else {
                        setValue(e.target.value)
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === "ArrowDown") {
                        e.preventDefault()
                        setOpen(true)
                      }
                    }}
                  />
                  <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        className="absolute top-1/2 right-2 size-6 -translate-y-1/2"
                      >
                        <CalendarIcon className="size-3.5" />
                        <span className="sr-only">选择日期</span>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="end">
                      <Calendar
                        mode="single"
                        selected={date}
                        month={month}
                        onMonthChange={setMonth}
                        locale={zhCN}
                        onSelect={(newDate) => {
                          if (newDate) {
                            handleDateChange(newDate)
                            setOpen(false)
                          }
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* 流时 */}
              <div className="flex-1">
                <Select 
                  value={selectedTimeIndex === -1 ? "" : selectedTimeIndex.toString()}
                  onValueChange={handleTimeChange}
                  disabled={!date}
                >
                  <SelectTrigger className="w-full text-xs md:text-sm">
                    <SelectValue placeholder="时辰" />
                  </SelectTrigger>
                  <SelectContent className="text-xs md:text-sm">
                    {timeList.map(([name, time, index]) => (
                      <SelectItem key={index} value={index.toString()}>{time}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }
)

Calendar24.displayName = "Calendar24"

interface FortuneTimeProps {
  astroData: FunctionalAstrolabe
  onSelectChange?: (fortuneType: FortuneType) => void
  onFortuneRequestChange?: (request: ZiweiFortuneRequest) => void
}

export const FortuneTime = React.forwardRef<FortuneTimeRef, FortuneTimeProps>(
  ({ astroData, onSelectChange, onFortuneRequestChange }, ref) => {
    const calendar24Ref = React.useRef<FortuneTimeRef>(null)

    React.useImperativeHandle(ref, () => ({
      getCurrentSelection: () => {
        return calendar24Ref.current?.getCurrentSelection() || {
          selectedDecadal: -1,
          selectedYear: -1,
          selectedMonth: -1,
          selectedDate: undefined,
          selectedTimeIndex: -1,
          createFortuneRequest: () => ({ type: '大限', date: new Date(), timeIndex: 6 })
        }
      },
      setDefaultValues: (fortuneRequest: ZiweiFortuneRequest) => {
        calendar24Ref.current?.setDefaultValues(fortuneRequest)
      }
    }))

    return (
      <div className="flex w-full">
        <Calendar24 
          ref={calendar24Ref}
          astroData={astroData} 
          onSelectChange={onSelectChange}
          onFortuneRequestChange={onFortuneRequestChange}
        />
      </div>
    )
  }
)

FortuneTime.displayName = "FortuneTime"

