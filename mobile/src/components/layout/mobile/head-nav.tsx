"use client"

import { useAuth } from "@/hooks/use-auth"
import { ChevronLeft, } from "lucide-react"
import { Button } from "@/components/ui/button"
import { usePathname, useRouter } from 'next/navigation'

const pageNames: Record<string, string> = {
  '/': '玄学汇',
  '/bazi': '八字',
  '/ziwei': '紫微斗数', 
  '/meihua': '梅花易数',
  '/user': '我的',
  '/bazi/divinate': '八字测算',
  '/ziwei/divinate': '紫微测算',
  '/meihua/divinate': '梅花测算',
  '/book': '经典书籍',
  '/course': '课程学习',
  '/cases': '案例解析'
}

export default function MobileNav({title}: {title:string}) {
  const pathname = usePathname()
  const router = useRouter()
  
  const { user, isAuthenticated } = useAuth()
  // 获取页面标题
  const getPageTitle = () => {
    // 精确匹配
    if (pageNames[pathname]) {
      return pageNames[pathname]
    }
    
    // 模糊匹配
    for (const [path, title] of Object.entries(pageNames)) {
      if (pathname.startsWith(path) && path !== '/') {
        return title
      }
    }
    
    return ''
  }

  // 判断是否显示返回按钮
  const shouldShowBackButton = () => {
    const mainPaths = ['/', '/bazi', '/ziwei', '/meihua', '/user']
    return !mainPaths.includes(pathname)
  }

  // 处理返回
  const handleBack = () => {
    if (typeof window !== 'undefined' && window.history.length > 1) {
      router.back()
    } else {
      router.push('/')
    }
  }

  return (
      <div className="flex w-full mobile-top-nav items-center justify-between px-4">
        {/* 左侧：返回按钮或占位 */}
        <div className="flex items-center w-14">
          {shouldShowBackButton() ? (
            <Button
              variant="ghost"
              onClick={handleBack}
              className="p-0 [&_svg]:size-5 gap-1"
              aria-label="返回"
            >
              <ChevronLeft size={24} color="#324258"/>
              <span className="font-normal text-foreground font-serif">返回</span>
            </Button>
          ) : null}
        </div>

        {/* 中间：页面标题 */}
        <div className="flex-1 text-center">
          <h1 className="text-base font-normal text-foreground truncate px-2">
            {title || getPageTitle()}
          </h1>
        </div>

        <div className="w-14"></div>

        {/* 右侧：用户头像和设置
        <div className="flex items-center space-x-2">
          {isAuthenticated && user ? (
            <Link href="/user">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/avatars/default-avatar.png" />
                <AvatarFallback className="text-xs">
                  {user.name?.[0]?.toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
            </Link>
          ) : (
            <Link href="/login">
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <User className="h-4 w-4" />
              </Button>
            </Link>
          )}
        </div> */}
      </div>
  )
}