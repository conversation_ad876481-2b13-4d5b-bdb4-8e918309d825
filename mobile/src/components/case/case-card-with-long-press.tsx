"use client"

import React from "react"
import { CaseProfile } from "@/types/case"
import { useLongPress } from "@/hooks/use-long-press"
import CaseCard from "./case-card"

interface CaseCardWithLongPressProps {
  profile: CaseProfile
  onClick?: (profile: CaseProfile) => void
  onLongPress?: (profile: CaseProfile) => void
  className?: string
}

const CaseCardWithLongPress: React.FC<CaseCardWithLongPressProps> = ({ 
  profile, 
  onClick,
  onLongPress,
  className = ""
}) => {
  // 使用长按功能
  const { bind } = useLongPress({
    threshold: 600,
    onClick: (id) => {
      if (onClick && id === profile.case_id) {
        onClick(profile)
      }
    },
    onLongPress: (id) => {
      if (onLongPress && id === profile.case_id) {
        onLongPress(profile)
      }
    },
  })

  return (
    <div {...bind}>
      <CaseCard 
        profile={profile}
        onClick={undefined} // 不使用 CaseCard 自己的 onClick，而是通过长按管理器处理
        className={className}
      />
    </div>
  )
}

CaseCardWithLongPress.displayName = 'CaseCardWithLongPress'
export default CaseCardWithLongPress
