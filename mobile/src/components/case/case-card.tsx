"use client"

import React from "react"
import { CaseProfile } from "@/types/case"
import dayjs from "dayjs"
import { UserIcon } from "@/components/icons/basic"

interface CaseCardProps {
  profile: CaseProfile
  onClick?: (profile: CaseProfile) => void
  className?: string
}

const CaseCard: React.FC<CaseCardProps> = ({ 
  profile, 
  onClick,
  className = ""
}) => {
  // 统一的样式主题
  const getUnifiedTheme = () => {
    return {
      backgroundImage: "url('/images/case_face.png')",
      border: 'border-gray-200/40',
      name: 'text-gray-800',
      info: 'text-gray-600',
      avatar: 'from-gray-200 to-slate-300',
      avatarText: 'text-gray-800',
      tag: 'bg-paper text-slate-600'
    }
  }
  
  // 格式化性别显示
  const formatGender = (gender: string) => {
    return gender === 'male' || gender === '男' ? '男' : 
           gender === 'female' || gender === '女' ? '女' : '其他'
  }
  const getGenderColor = (gender: string) => {
    return gender === 'male' || gender === '男' ? 'text-blue-600' : 
           gender === 'female' || gender === '女' ? 'text-pink-600' : 'text-primary'
  }

  const theme = getUnifiedTheme()

  const handleClick = () => {
    if (onClick) {
      onClick(profile)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.key === 'Enter' || e.key === ' ') && onClick) {
      e.preventDefault()
      onClick(profile)
    }
  }

  return (
    <div 
      data-item-id={profile.case_id}
      className={`
        flex flex-col group relative overflow-hidden rounded-sm ${theme.border} items-center justify-center
        transition-all duration-300 ease-out transform-gpu aspect-[3/4]
        select-none no-callout no-drag
        ${onClick ? 'cursor-pointer active:scale-[0.98] hover:scale-[1.02] hover:-translate-y-1' : ''}
        backdrop-blur-sm ${className}
      `}
      style={{
        //backgroundImage: theme.backgroundImage,
        backgroundColor: 'white',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        touchAction: 'none'  // 关键：禁用浏览器的触摸手势，确保事件正确传播
      }}
      // onClick={handleClick}
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : undefined}
      // onKeyDown={handleKeyDown}
      aria-label={onClick ? `查看${profile.user_name}的档案` : undefined}
    >
      {/* 背景覆盖层 - 确保文字可读性 */}
      <div className="absolute inset-0 bg-paper/10 backdrop-blur-[1px]" />
      
      {/* 主要内容区域 - 优化3列布局间距 */}
      <div className="relative flex flex-col items-center justify-center px-2 py-8 space-y-2">
        <div className="flex flex-col items-center justify-center space-y-1">
        {/* 头像和名称 - 3列布局优化 */}
        <div className="flex items-center justify-center ">
          {/* 名称 - 突出显示，适配小空间 */}
          <p className={`
            text-base font-bold ${theme.name} text-center leading-tight
            tracking-wide font-sans truncate w-full px-1
          `}>
            {profile.user_name}
          </p>
          <UserIcon size="xs" className={`${getGenderColor(profile.gender)} flex-shrink-0 absolute right-0 mr-1`} />
        </div>

        {/* 基本信息 - 紧凑布局 */}
        <div className="space-y-1 text-center">
          <p className={`text-[10px] ${theme.info} leading-relaxed`}>
            {dayjs(profile.birth_time_solar).format('YYYY-MM-DD')}
          </p>
        </div>
        </div>

        {/* 标签区域 - 3列布局优化 */}
        {profile.tags && profile.tags.length > 0 && (
          <div className="flex flex-wrap justify-center gap-1 min-h-[1.25rem]">
            {profile.tags.slice(0, 2).map((tag, index) => (
              <span 
                key={index}
                className={`
                  inline-flex items-center px-2 py-0.5 rounded-xl
                  text-[9px] ${theme.tag}
                  border transition-colors duration-200
                  truncate
                `}
                title={tag}
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* 悬浮效果装饰 */}
      <div className="absolute inset-0 bg-white/0 group-hover:bg-white/20 transition-colors duration-300 rounded-2xl" />
      
      {/* 点击指示器 */}

      {/* 底部装饰线 */}
      <div className={`absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r ${theme.avatar} opacity-30`} />
    </div>
  )
}

CaseCard.displayName = 'CaseCard'
export default CaseCard