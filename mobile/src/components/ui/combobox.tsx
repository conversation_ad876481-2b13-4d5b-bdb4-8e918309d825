"use client"

import * as React from "react"

// import { useIsMobile } from "@/hooks/use-mobile"
import { useDebounce } from "@/hooks/use-debounce"
import { But<PERSON> } from "@/components/ui/button"
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Drawer,
  DrawerContent,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { LocationItem } from "@/types/user"

interface ComboBoxResponsiveProps {
  placeholder: string
  jsonUrl?: string // JSON文件URL
  onSelect?: (item: LocationItem | null) => void
  value?: LocationItem | null
}

export function ComboBoxResponsive({
  placeholder,
  jsonUrl = '/assets/location.json',
  onSelect,
  value
}: ComboBoxResponsiveProps) {
  const [open, setOpen] = React.useState(false)
  // const isMobile = useIsMobile()
  const [selectedStatus, setSelectedStatus] = React.useState<LocationItem | null>(value || null)
  const [allData, setAllData] = React.useState<LocationItem[]>([])
  const [filteredData, setFilteredData] = React.useState<LocationItem[]>([])
  const [searchValue, setSearchValue] = React.useState("")
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  
  // 使用防抖优化搜索性能
  const debouncedSearchValue = useDebounce(searchValue, 300)

  // 加载JSON数据
  React.useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        const response = await fetch(jsonUrl)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data: LocationItem[] = await response.json()
        
        // 验证数据格式
        if (!Array.isArray(data)) {
          throw new Error('JSON数据格式错误：应该是数组格式')
        }
        
        // 验证数据项格式
        const validData = data.filter(item => 
          item && 
          typeof item === 'object' && 
          typeof item.label === 'string' && 
          typeof item.value !== 'undefined'
        )
        
        if (validData.length !== data.length) {
          console.warn(`过滤了 ${data.length - validData.length} 条无效数据`)
        }
        
        setAllData(validData)
        // 初始显示前10条
        setFilteredData(validData.slice(0, 10))
        
      } catch (err) {
        console.error('加载位置数据失败:', err)
        setError(err instanceof Error ? err.message : '加载数据失败')
        // 使用备用数据
        const fallbackData: LocationItem[] = [
          { value: "116.407526", label: "北京 北京市" },
          { value: "121.473701", label: "上海 上海市" },
          { value: "113.264385", label: "广东 广州市" },
          { value: "114.305392", label: "广东 深圳市" },
        ]
        setAllData(fallbackData)
        setFilteredData(fallbackData)
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [jsonUrl])

  // 搜索过滤逻辑
  const filterData = React.useCallback((query: string) => {
    if (!query.trim()) {
      // 没有搜索词时显示前10条
      return allData.slice(0, 10)
    }

    const searchTerm = query.toLowerCase().trim()
    
    // 使用模糊搜索，支持拼音首字母等
    const filtered = allData.filter(item => 
      item.label.toLowerCase().includes(searchTerm) ||
      item.value.toString().includes(searchTerm)
    )

    // 返回前10条匹配结果
    return filtered.slice(0, 10)
  }, [allData])

  // 处理搜索输入变化
  const handleSearchChange = React.useCallback((value: string) => {
    setSearchValue(value)
  }, [])

  // 使用防抖后的搜索值进行过滤
  React.useEffect(() => {
    const filtered = filterData(debouncedSearchValue)
    setFilteredData(filtered)
  }, [debouncedSearchValue, filterData])

  // 处理选择
  const handleSelect = React.useCallback((selectedItem: LocationItem | null) => {
    setSelectedStatus(selectedItem)
    onSelect?.(selectedItem)
    setOpen(false)
    setSearchValue("") // 清空搜索
  }, [onSelect])

  // 当组件打开时重置搜索
  React.useEffect(() => {
    if (open) {
      setSearchValue("")
      setFilteredData(filterData(""))
    }
  }, [open, filterData])

  // if (!isMobile) {
  //   return (
  //      <div className="h-10">
  //       <Popover open={open} onOpenChange={setOpen}>
  //         <PopoverTrigger asChild>
  //           <Button 
  //             variant="outline" 
  //             className="w-[280px] rounded-xl h-10 justify-start text-base bg-white hover:bg-white hover:text-clip hover:text-muted-foreground font-normal"
  //             disabled={isLoading}
  //           >
  //             {isLoading ? (
  //               <>加载中...</>
  //             ) : selectedStatus ? (
  //               <div className="truncate hover:text-clip text-base">{selectedStatus.label}</div>
  //             ) : (
  //               <>{placeholder}</>
  //             )}
  //           </Button>
  //         </PopoverTrigger>
  //         <PopoverContent className="p-0 w-[280px]" align="start">
  //           <StatusList 
  //             data={filteredData}
  //             searchValue={searchValue}
  //             onSearchChange={handleSearchChange}
  //             setOpen={setOpen} 
  //             setSelectedStatus={handleSelect}
  //             isLoading={isLoading}
  //             error={error}
  //           />
  //         </PopoverContent>
  //       </Popover>
  //     </div>
  //   )
  // }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button 
          variant="outline" 
          className="w-full justify-start bg-white hover:bg-white hover:text-clip hover:text-muted-foreground font-normal"
          disabled={isLoading}
        >
          {isLoading ? (
            <>加载中...</>
          ) : selectedStatus ? (
            <>{selectedStatus.label}</>
          ) : (
            <>{placeholder}</>
          )}
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <VisuallyHidden>
          <DrawerTitle>选择出生地</DrawerTitle> 
        </VisuallyHidden>
        <div className="mt-4 border-t">
          <StatusList 
            data={filteredData}
            searchValue={searchValue}
            onSearchChange={handleSearchChange}
            setOpen={setOpen} 
            setSelectedStatus={handleSelect}
            isLoading={isLoading}
            error={error}
          />
        </div>
      </DrawerContent>
    </Drawer>
  )
}

interface StatusListProps {
  data: LocationItem[]
  searchValue: string
  onSearchChange: (value: string) => void
  setOpen: (open: boolean) => void
  setSelectedStatus: (status: LocationItem | null) => void
  isLoading: boolean
  error: string | null
}

function StatusList({
  data,
  searchValue,
  onSearchChange,
  setOpen,
  setSelectedStatus,
  isLoading,
  error
}: StatusListProps) {
  return (
    <Command shouldFilter={false}> {/* 禁用内置过滤，使用自定义过滤 */}
      <CommandInput 
        placeholder="搜索城市..." 
        value={searchValue}
        onValueChange={onSearchChange}
        className="text-base"
      />
      <CommandList>
        {isLoading ? (
          <div className="py-6 text-center text-sm text-muted-foreground">
            正在加载数据...
          </div>
        ) : error ? (
          <div className="py-6 text-center text-sm text-red-500">
            {error}
          </div>
        ) : data.length === 0 ? (
          <CommandEmpty>
            {searchValue ? "未找到匹配的城市" : "暂无数据"}
          </CommandEmpty>
        ) : (
          <CommandGroup>
            {data.map((item, index) => (
              <CommandItem
                key={`${item.value}-${index}`}
                value={item.label}
                onSelect={() => {
                  setSelectedStatus(item)
                  setOpen(false)
                }}
                className="cursor-pointer"
              >
                <div className="flex flex-col">
                  <span className="font-medium">{item.label}</span>
                </div>
              </CommandItem>
            ))}
            {data.length === 10 && (
              <div className="py-2 text-center text-xs text-muted-foreground border-t">
                请输入关键词搜索更多...
              </div>
            )}
          </CommandGroup>
        )}
      </CommandList>
    </Command>
  )
}
