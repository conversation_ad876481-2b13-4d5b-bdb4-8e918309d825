import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON>eader, DrawerTitle } from '@/components/ui/drawer'
import { DateInput } from '@heroui/date-input'
import { DatePickerView } from 'antd-mobile'
import { ZonedDateTime, parseZonedDateTime } from '@internationalized/date'
import { I18nProvider } from '@react-aria/i18n'

export function DateTimePicker({value, onSelect}: {value: string | null, onSelect: (value: string) => void}) {
    const [isDrawerOpen, setIsDrawerOpen] = React.useState(false)
    // 统一用 ZonedDateTime 作为单一数据源
    const [date, setDate] = React.useState<ZonedDateTime | null>(null);
    // DateInput onChange
    const handleDateInputChange = (newDate: ZonedDateTime | null) => {
        setDate(newDate);
        onSelect(formatDate(newDate) || "");
    };

    // DatePickerView onChange
    const handleDatePickerChange = (value: Date) => {
        if (value) {
            const iso = value.toISOString().slice(0, 16); // "2021-04-07T18:45"
            const zoned = parseZonedDateTime(`${iso}[Asia/Shanghai]`);
            setDate(zoned);
            onSelect(formatDate(zoned) || "");
        }
    };
    const labelRenderer = (type: string, data: number) => {
        switch (type) {
          case 'year':
            return data + '年'
          case 'month':
            return data + '月'
          case 'day':
            return data + '日'
          case 'hour':
            return data + '时'
          case 'minute':
            return data + '分'
          default:
            return data
        }
      }

    // DatePickerView 需要 JS Date 类型
    const datePickerValue = React.useMemo(() => {
        if (!date) return undefined;
        return new Date(
            date.year,
            date.month - 1,
            date.day,
            date.hour,
            date.minute
        );
    }, [date]);

    const handleClose = () => setIsDrawerOpen(false);

    const formatDate = (date: ZonedDateTime | null) => {
      if (!date) return '';
      const pad = (n: number) => n.toString().padStart(2, '0');
      return `${date.year}-${pad(date.month)}-${pad(date.day)} ${pad(date.hour)}:${pad(date.minute)}`;
    };

    return (
        <div className="flex items-center w-full justify-center font-sans">
            <div className="w-full space-y-4 bg-white rounded-lg ">
                <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen} onClose={handleClose} aria-describedby="birthtime" modal={true} handleOnly={true}>
                    <DrawerTrigger asChild>
                        <Button variant="outline" className="w-full justify-start shadow-none font-normal text-base" value="选择出生时间">
                            {formatDate(date) || "选择出生时间"}
                        </Button>
                    </DrawerTrigger>
                    <DrawerContent>
                        <DrawerHeader className='px-0 pb-1'>
                            <DrawerTitle className="flex w-full">
                                <I18nProvider locale="zh-CN">
                                <DateInput
                                    aria-describedby="birthtime"
                                    granularity="minute"
                                    value={date}
                                    onChange={handleDateInputChange}
                                    aria-label="选择出生日期"
                                    hideTimeZone={true}
                                    classNames={{
                                        input: 'justify-center text-base',
                                    }}
                                />
                                </I18nProvider>
                            </DrawerTitle>
                        </DrawerHeader>
                        <DatePickerView
                            value={datePickerValue}
                            defaultValue={new Date()}
                            precision="minute"
                            mouseWheel={true}
                            onChange={handleDatePickerChange}
                            renderLabel={labelRenderer}
                            min={new Date(1000, 0, 1)}
                            max={new Date(2035, 11, 31)}
                        />
                    </DrawerContent>
                </Drawer>
            </div>
        </div>
    );
}

                                                               
                                                               
                                                               
                                                               
                                                               
                                                               
                                                               
                                                               