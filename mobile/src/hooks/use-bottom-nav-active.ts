// hooks/useBottomNavActive.ts
'use client';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

function getActiveTab(pathname:string){
    const pathname_keys = pathname.split('/')
    const category = pathname_keys[1];
    if (pathname_keys.length > 2) {
      const tab = pathname_keys[2];
      if (NAV_KEYS.includes(tab as NavKey)) {
        return (tab as NavKey);
      }
    }
    return NAV_KEYS.includes(category as NavKey) ? (category as NavKey) : null;
}

const NAV_KEYS = ['bazi', 'ziwei', 'meihua', 'divinate', 'user'] as const;
type NavKey = (typeof NAV_KEYS)[number];

export function useBottomNavActive() {
  const pathname = usePathname();
  const router = useRouter();

  const [pendingNav, setPendingNav] = useState<NavKey | null>(null);
  const [currentNav, setCurrentNav] = useState<NavKey | null>(() => {
    return getActiveTab(pathname)
  });

  useEffect(() => {
    const activeTab = getActiveTab(pathname)
    if (activeTab) {
      setCurrentNav(activeTab as NavKey);
      setPendingNav(null);
    }
  }, [pathname]);

  const handleNavClick = (key: NavKey) => {
    // 只有当当前路径完全等于导航键时才跳过（例如：/divinate 而不是 /divinate/result）
    if (key === currentNav && pathname === `/${key}`) {
      return;
    }

    if (key === 'divinate') {
      setPendingNav(key);
      // 添加案例的特殊处理
      try {
        const lastVisitedTab = typeof window !== 'undefined' ? localStorage.getItem('lastVisitedTab') : null
        if (lastVisitedTab) {
          router.push(`/${lastVisitedTab}/divinate`)
        } else {
          router.push('/bazi/divinate')
        }
      } catch (e) {
        router.push('/bazi/divinate')
      }
    } else {
      setPendingNav(key);
      // 保存用户最后访问的tab到localStorage（仅在客户端）
      if (['bazi', 'ziwei', 'meihua'].includes(key)) {
        try {
          if (typeof window !== 'undefined') {
            localStorage.setItem('lastVisitedTab', key)
          }
          router.push(`/${key}/case`);
        } catch (e) {
          router.push(`/${key}/case`);
        }
      } else {
        router.push(`/user`);
      }
    }
  };

  const activeNav = pendingNav || currentNav;

  return { activeNav, handleNavClick };
}
