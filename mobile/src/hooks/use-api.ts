"use client"

import useSWR from 'swr'
import useSWRMutation from 'swr/mutation'
import { booksAPI, casesAPI, createFetcher } from '@/lib/api/api-client'
import { useAuth } from './use-auth'
import { Toast } from 'antd-mobile'
import { CaseCreateRequest, CaseUpdateRequest } from '@/types/case'
import { CaseType, RelationType } from '@/types/enums'
import { BirthData } from '@/types/user'

// 通用SWR配置
const defaultConfig = {
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  shouldRetryOnError: false,
  errorRetryCount: 2,
}

// 书籍相关hooks
export function useBookList(params: {
  page?: string
  size?: string
  is_completed?: string
  category?: string | null
} = {}) {
  const key = ['/book/list', params]
  
  const { data, error, isLoading, mutate } = useSWR(
    key,
    () => booksAPI.getBooks(params),
    defaultConfig
  )

  return {
    books: data?.items || [],
    total: data?.total || 0,
    totalPages: data?.total_pages || 0,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

export function useBookDetail(bookId: string | null) {
  // const { token } = useAuth()
  // const key = token ? ['/book', bookId] : null
  const key = bookId ? ['/book', bookId] : null
  
  const { data, error, isLoading, mutate } = useSWR(
    key,
    // () => booksAPI.getBookDetail(bookId, token || undefined),
    () => booksAPI.getBookDetail(bookId, undefined),
    defaultConfig
  )

  return {
    chapters: data?.chapter_list || [],
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

export function useChapterContent(chapterId: string | null) {
  //const { token } = useAuth()
  //const key = token && chapterId ? ['/book/chapter', chapterId] : null
  const key = chapterId ? ['/book/chapter', chapterId] : null

  const { data, error, isLoading, mutate } = useSWR(
    key,
    //() => booksAPI.getChapterContent(chapterId, token || undefined),
    () => booksAPI.getChapterContent(chapterId, undefined),
    defaultConfig
  )
  return {
    content: data?.chapters,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

// 案例相关hooks
// 案例列表
export function useCaseList(params: any = {}) {
  const { token } = useAuth()
  const key = token ? ['/cases/list', params] : null

  const { data, error, isLoading, mutate } = useSWR(
    key,
    () => casesAPI.getCases(params, token || undefined),
    defaultConfig
  )

  return {
    cases: data?.items || [],
    total: data?.total || 0,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

// 案例详情
export function useCaseDetail(caseId: string | null) {
  const { token } = useAuth()
  const key = token && caseId ? ['/cases', caseId] : null

  const { data, error, isLoading, mutate } = useSWR(
    key,
    () => casesAPI.getCaseDetail(caseId!, token || undefined),
    defaultConfig
  )

  return {
    case: data,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
}

// 创建案例的 mutation
export function useCreateCase() {
  const { token } = useAuth()

  // 从BirthData和astroData创建CaseCreateRequest的辅助函数
  const createCaseRequestFromData = (
    birthData: BirthData, 
    astroData: any, 
    caseType: CaseType
  ): CaseCreateRequest => {
    // 从astroData.chineseDate解析八字信息（空格分割的字符串）
    const chineseDateParts = astroData.chineseDate ? astroData.chineseDate.split(' ') : ['', '', '', '']
    const baziInfo = {
      year: chineseDateParts[0] || '',
      month: chineseDateParts[1] || '',
      day: chineseDateParts[2] || '',
      time: chineseDateParts[3] || ''
    }

    return {
      user_name: birthData.name,
      gender: birthData.gender,
      birth_time_solar: birthData.birthTime + ':00',  // 确保格式正确
      birth_time_lunar: astroData.lunarDate + ' ' + astroData.time || '', // 从astroData获取农历时间
      bazi_year: baziInfo.year,
      bazi_month: baziInfo.month,
      bazi_day: baziInfo.day,
      bazi_time: baziInfo.time,
      birth_place: birthData.birthplace,
      useTrueSolarTime: birthData.useTrueSolarTime,
      isDST: birthData.isDST,
      useEarlyOrLateNight: birthData.useEarlyOrLateNight,
      longitude: birthData.longitude,
      divinate_result: null, // 改为null而不是空字符串
      relation_type: birthData.relationship as RelationType,
      tags: [],
      case_type: caseType
    }
  }

  return useSWRMutation(
    '/api/case/new',
    async (key: string, { arg }: { arg: { birthData: BirthData; astroData: any; caseType: CaseType } }) => {
      try {
        const caseRequest = createCaseRequestFromData(arg.birthData, arg.astroData, arg.caseType)
        const response = await casesAPI.createCase(caseRequest, token || undefined)
        return response || null
      } catch (error) {
        Toast.show({
          content: error instanceof Error ? error.message : '创建案例失败',
          icon: 'fail',
        })
        throw error
      }
    }
  )
}
// 删除案例
export function useDeleteCase() {
  const { token } = useAuth()

  return useSWRMutation(
    '/api/case/delete',
    async (key: string, { arg }: { arg: { caseId: string } }) => {
      try {
        const result = await casesAPI.deleteCase(arg.caseId, token || undefined)
        return result?.success || false
      } catch (error) {
        Toast.show({
          content: error instanceof Error ? error.message : '删除案例失败',
          icon: 'fail',
        })
        throw error
      }
    }
  )
}

// 更新案例的 mutation
export function useUpdateCase() {
  const { token } = useAuth()

  return useSWRMutation(
    '/api/case/update',
    async (key: string, { arg }: { arg: { caseId: string; updates: Partial<CaseUpdateRequest> } }) => {
      try {
        const result = await casesAPI.updateCase(arg.caseId, arg.updates, token || undefined)
        return result?.success || false
      } catch (error) {
        Toast.show({
          content: error instanceof Error ? error.message : '更新案例失败',
          icon: 'fail',
        })
        throw error
      }
    }
  )
}

// 通用数据获取hook
export function useData<T>(
  endpoint: string | null,
  requireAuth: boolean = false
) {
  const { token } = useAuth()
  const fetcher = createFetcher(requireAuth ? token || undefined : undefined)
  
  // 如果需要认证但没有token，不发起请求
  const key = requireAuth && !token ? null : endpoint
  
  const { data, error, isLoading, mutate } = useSWR(
    key,
    fetcher,
    defaultConfig
  )

  return {
    data,
    isLoading,
    isError: !!error,
    error,
    mutate
  }
} 