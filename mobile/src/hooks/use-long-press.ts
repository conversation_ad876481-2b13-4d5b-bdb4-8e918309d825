'use client'

import { useRef, MouseEvent, TouchEvent, useCallback } from 'react'

interface UseLongPressOptions {
    threshold?: number
    onClick?: (id: string) => void
    onLongPress?: (id: string) => void
}

export function useLongPress({
    threshold = 600,
    onClick,
    onLongPress
}: UseLongPressOptions) {
    const timerRef = useRef<NodeJS.Timeout | null>(null)
    const isLongPressTriggered = useRef(false)
    const targetItemId = useRef<string | null>(null)

    // 开始按下
    const handlePressStart = 
        (e: MouseEvent | TouchEvent) => {
            console.log('handlePressStart  start')
            const targetElement = (e.target as HTMLElement).closest('[data-item-id]')
            if (!targetElement) return

            console.log('handlePressStart', e)
            const id = targetElement.getAttribute('data-item-id')
            if (!id) return

            targetItemId.current = id
            isLongPressTriggered.current = false

            timerRef.current = setTimeout(() => {
                isLongPressTriggered.current = true
                onLongPress?.(id)
            }, threshold)
        }

    // 松开
    const handlePressEnd = (event: MouseEvent | TouchEvent) => {
        console.log('handlePressEnd  start')
        if (timerRef.current) {
            clearTimeout(timerRef.current)
        }

        // 如果是长按触发的，就在事件结束时阻止默认行为（如移动端菜单）
        console.log('isLongPressTriggered', isLongPressTriggered.current)
        if (isLongPressTriggered.current) {
            console.log('handlePressEnd', event)
            // 检查事件是否可以取消，避免 Intervention 错误
            if (event.cancelable) {
                event.preventDefault();
            }
        }

        if (!isLongPressTriggered.current && targetItemId.current) {
            onClick?.(targetItemId.current)
        }

        targetItemId.current = null
    }

    // 取消（手指/鼠标移出）
    const handlePressCancel = () => {
        if (timerRef.current) {
            console.log('handlePressCancel', timerRef.current)
            clearTimeout(timerRef.current)
        }
        targetItemId.current = null
    }

    // 关键：阻止浏览器默认的长按菜单
    const handleContextMenu = (e: MouseEvent) => {
        e.preventDefault()
    }


    return {
        bind: {
            onMouseDown: handlePressStart,
            onTouchStart: handlePressStart,
            onMouseUp: handlePressEnd,
            onTouchEnd: handlePressEnd,
            onTouchMove: handlePressCancel,
            onMouseLeave: handlePressCancel,
            onContextMenu: handleContextMenu,
        },
    }
}