'use client'

import { useRef, MouseEvent, TouchEvent, useCallback } from 'react'

interface UseLongPressOptions {
    threshold?: number
    onClick?: (id: string) => void
    onLongPress?: (id: string) => void
}

export function useLongPress({
    threshold = 600,
    onClick,
    onLongPress
}: UseLongPressOptions) {
    const timerRef = useRef<NodeJS.Timeout | null>(null)
    const isLongPressTriggered = useRef(false)
    const targetItemId = useRef<string | null>(null)
    const startPosition = useRef<{ x: number; y: number } | null>(null)
    const moveThreshold = 10 // 移动超过10px才算移出

    // 开始按下
    const handlePressStart = useCallback((e: MouseEvent | TouchEvent) => {
        console.log('handlePressStart start')

        // 阻止默认行为，特别是图片的长按菜单
        e.preventDefault()

        const targetElement = (e.target as HTMLElement).closest('[data-item-id]')
        if (!targetElement) return

        console.log('handlePressStart', e.type)
        const id = targetElement.getAttribute('data-item-id')
        if (!id) return

        // 记录开始位置
        if ('touches' in e) {
            startPosition.current = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            }
        } else {
            startPosition.current = {
                x: e.clientX,
                y: e.clientY
            }
        }

        targetItemId.current = id
        isLongPressTriggered.current = false

        // 清除之前的定时器
        if (timerRef.current) {
            clearTimeout(timerRef.current)
        }

        timerRef.current = setTimeout(() => {
            isLongPressTriggered.current = true
            console.log('Long press triggered for:', id)
            onLongPress?.(id)
        }, threshold)
    }, [threshold, onLongPress])

    // 松开
    const handlePressEnd = useCallback((event: MouseEvent | TouchEvent) => {
        console.log('handlePressEnd start')

        if (timerRef.current) {
            clearTimeout(timerRef.current)
            timerRef.current = null
        }

        // 如果是长按触发的，阻止默认行为
        if (isLongPressTriggered.current) {
            console.log('handlePressEnd - long press was triggered')
            if (event.cancelable) {
                event.preventDefault()
                event.stopPropagation()
            }
        } else if (targetItemId.current) {
            // 短按处理
            console.log('handlePressEnd - short press, calling onClick')
            onClick?.(targetItemId.current)
        }

        // 重置状态
        targetItemId.current = null
        startPosition.current = null
        isLongPressTriggered.current = false
    }, [onClick])

    // 移动处理 - 只有移动距离超过阈值才取消
    const handlePressMove = useCallback((e: TouchEvent) => {
        if (!startPosition.current || !timerRef.current) return

        const currentTouch = e.touches[0]
        const deltaX = Math.abs(currentTouch.clientX - startPosition.current.x)
        const deltaY = Math.abs(currentTouch.clientY - startPosition.current.y)
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

        // 只有移动距离超过阈值才取消长按
        if (distance > moveThreshold) {
            console.log('handlePressMove - moved too far, cancelling')
            if (timerRef.current) {
                clearTimeout(timerRef.current)
                timerRef.current = null
            }
            targetItemId.current = null
            startPosition.current = null
        }
    }, [moveThreshold])

    // 鼠标离开处理
    const handleMouseLeave = useCallback(() => {
        console.log('handleMouseLeave')
        if (timerRef.current) {
            clearTimeout(timerRef.current)
            timerRef.current = null
        }
        targetItemId.current = null
        startPosition.current = null
    }, [])

    // 阻止右键菜单
    const handleContextMenu = useCallback((e: MouseEvent) => {
        console.log('handleContextMenu - preventing default')
        e.preventDefault()
        e.stopPropagation()
    }, [])

    return {
        bind: {
            onMouseDown: handlePressStart,
            onTouchStart: handlePressStart,
            onMouseUp: handlePressEnd,
            onTouchEnd: handlePressEnd,
            onTouchMove: handlePressMove,
            onMouseLeave: handleMouseLeave,
            onContextMenu: handleContextMenu,
            // 添加拖拽相关的阻止
            onDragStart: (e: React.DragEvent) => e.preventDefault(),
        },
    }
}